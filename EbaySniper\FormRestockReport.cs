using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst.Restocker.Data;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst
{
    /// <summary>
    /// Form for generating and displaying restock transaction reports
    /// </summary>
    public partial class FormRestockReport : XtraForm
    {
        private readonly IPurchaseTrackerRepository _repository;
        private readonly IReportService _reportService;
        private readonly IReportExportService _exportService;
        private List<PurchaseTransaction> _currentTransactions;

        public FormRestockReport()
        {
            InitializeComponent();

            // Initialize services
            _repository = new PurchaseTrackerRepository();
            _reportService = new ReportService(_repository);
            _exportService = new ReportExportService();

            // Set default values
            spinEditDays.Value = 30;
            _currentTransactions = new List<PurchaseTransaction>();

            // Configure grid
            ConfigureGrid();
        }

        private void ConfigureGrid()
        {
            var gridView = gridControlTransactions.MainView as GridView;
            if (gridView != null)
            {
                gridView.OptionsView.ShowGroupPanel = false;
                gridView.OptionsView.ColumnAutoWidth = false;
                gridView.OptionsBehavior.Editable = false;
                gridView.OptionsSelection.MultiSelect = false;
            }
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                btnGenerateReport.Enabled = false;
                btnExport.Enabled = false;

                // Show loading
                lblStatus.Text = "Generating report...";

                var days = (int)spinEditDays.Value;
                var startDate = DateTime.Now.AddDays(-days);
                var endDate = DateTime.Now;

                // Create filter
                var filter = new ReportFilter
                {
                    StartDate = startDate,
                    EndDate = endDate,
                    IncludeCompleted = true,
                    IncludeFailed = true,
                    IncludePending = true
                };

                // Get transactions
                _currentTransactions = (await _repository.GetTransactionsByDateRangeAsync(startDate, endDate)).ToList();

                // Bind to grid
                gridControlTransactions.DataSource = _currentTransactions;

                // Update summary
                UpdateSummary();

                lblStatus.Text = $"Report generated successfully. Found {_currentTransactions.Count} transactions.";
                btnExport.Enabled = _currentTransactions.Count > 0;
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error generating report: {ex.Message}";
                XtraMessageBox.Show($"Error generating report: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnGenerateReport.Enabled = true;
            }
        }

        private void UpdateSummary()
        {
            if (_currentTransactions == null || !_currentTransactions.Any())
            {
                lblTotalTransactions.Text = "Total Transactions: 0";
                lblTotalAmount.Text = "Total Amount: $0.00";
                lblCompletedTransactions.Text = "Completed: 0";
                lblFailedTransactions.Text = "Failed: 0";
                return;
            }

            var totalTransactions = _currentTransactions.Count;
            var totalAmount = _currentTransactions.Where(t => t.Status == "Completed").Sum(t => t.PurchasePrice);
            var completedCount = _currentTransactions.Count(t => t.Status == "Completed");
            var failedCount = _currentTransactions.Count(t => t.Status == "Failed");

            lblTotalTransactions.Text = $"Total Transactions: {totalTransactions}";
            lblTotalAmount.Text = $"Total Amount: ${totalAmount:F2}";
            lblCompletedTransactions.Text = $"Completed: {completedCount}";
            lblFailedTransactions.Text = $"Failed: {failedCount}";
        }

        private async void btnExport_Click(object sender, EventArgs e)
        {
            if (_currentTransactions == null || !_currentTransactions.Any())
            {
                XtraMessageBox.Show("No data to export. Please generate a report first.", "No Data", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "CSV Files (*.csv)|*.csv|All Files (*.*)|*.*";
                    saveDialog.DefaultExt = "csv";
                    saveDialog.FileName = $"RestockReport_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        btnExport.Enabled = false;
                        lblStatus.Text = "Exporting report...";

                        // Create a simple transaction report for export
                        var report = new TransactionDetailReport
                        {
                            KeywordId = "All",
                            JobId = "All",
                            KeywordAlias = "All Restock Transactions",
                            GeneratedAt = DateTime.Now,
                            Transactions = _currentTransactions,
                            Attempts = new List<PurchaseAttempt>() // Empty list since we're only dealing with transactions
                        };

                        await _exportService.ExportTransactionDetailReportToCsvAsync(report, saveDialog.FileName);

                        lblStatus.Text = $"Report exported successfully to: {saveDialog.FileName}";
                        XtraMessageBox.Show($"Report exported successfully to:\n{saveDialog.FileName}", "Export Complete", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                lblStatus.Text = $"Error exporting report: {ex.Message}";
                XtraMessageBox.Show($"Error exporting report: {ex.Message}", "Export Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnExport.Enabled = true;
            }
        }

        private void FormRestockReport_Load(object sender, EventArgs e)
        {
            lblStatus.Text = "Ready. Select number of days and click Generate Report.";
        }

        private void FormRestockReport_FormClosed(object sender, FormClosedEventArgs e)
        {
            // Clean up resources if needed
            _repository?.Dispose();
        }

        private void spinEditDays_EditValueChanged(object sender, EventArgs e)
        {
            // Clear current results when days value changes
            if (_currentTransactions?.Any() == true)
            {
                lblStatus.Text = "Days value changed. Click Generate Report to refresh data.";
            }
        }
    }
}
